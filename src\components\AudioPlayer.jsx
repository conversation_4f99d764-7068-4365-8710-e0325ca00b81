import React, { useState, useRef, useEffect } from 'react';
import { FiPlay, FiPause, FiVolume2, FiVolumeX } from 'react-icons/fi';
import { convertMinioUrlToApiUrl } from '../services/files';

const AudioPlayer = ({ 
  audioUrl, 
  className = "",
  size = "sm", // sm, md, lg
  showDuration = false,
  autoPlay = false,
  onPlay = null,
  onPause = null,
  onEnded = null
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [error, setError] = useState(null);
  
  const audioRef = useRef(null);
  const progressRef = useRef(null);

  // Convert MinIO URL to playable URL
  const playableUrl = audioUrl ? convertMinioUrlToApiUrl(audioUrl) : null;

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
      setError(null);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      onEnded?.();
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };

    const handleError = (e) => {
      console.error('Audio error:', e);
      setError('Ошибка загрузки аудио');
      setIsLoading(false);
      setIsPlaying(false);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('error', handleError);
    audio.addEventListener('loadstart', handleLoadStart);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('loadstart', handleLoadStart);
    };
  }, [onPlay, onPause, onEnded]);

  useEffect(() => {
    if (autoPlay && playableUrl && audioRef.current) {
      handlePlayPause();
    }
  }, [autoPlay, playableUrl]);

  const handlePlayPause = async () => {
    if (!audioRef.current || !playableUrl) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        await audioRef.current.play();
      }
    } catch (error) {
      console.error('Playback error:', error);
      setError('Ошибка воспроизведения');
    }
  };

  const handleProgressClick = (e) => {
    if (!audioRef.current || !progressRef.current) return;

    const rect = progressRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const newTime = (clickX / width) * duration;
    
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (!audioRef.current) return;

    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  const formatTime = (time) => {
    if (!time || !isFinite(time)) return '0:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'lg':
        return {
          button: 'w-12 h-12',
          icon: 20,
          container: 'space-x-4'
        };
      case 'md':
        return {
          button: 'w-10 h-10',
          icon: 18,
          container: 'space-x-3'
        };
      case 'sm':
      default:
        return {
          button: 'w-8 h-8',
          icon: 16,
          container: 'space-x-2'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  if (!playableUrl) {
    return (
      <div className={`flex items-center text-gray-400 ${className}`}>
        <FiVolumeX size={sizeClasses.icon} />
        <span className="text-sm ml-2">Нет аудио</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center text-red-500 ${className}`}>
        <FiVolumeX size={sizeClasses.icon} />
        <span className="text-sm ml-2">{error}</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${sizeClasses.container} ${className}`}>
      {/* Audio element */}
      <audio
        ref={audioRef}
        src={playableUrl}
        preload="metadata"
      />

      {/* Play/Pause button */}
      <button
        onClick={handlePlayPause}
        disabled={isLoading}
        className={`
          flex items-center justify-center ${sizeClasses.button}
          bg-blue-600 text-white rounded-full hover:bg-blue-700 
          transition-colors disabled:opacity-50 disabled:cursor-not-allowed
        `}
        title={isPlaying ? 'Пауза' : 'Воспроизвести'}
      >
        {isLoading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
        ) : isPlaying ? (
          <FiPause size={sizeClasses.icon - 2} />
        ) : (
          <FiPlay size={sizeClasses.icon - 2} />
        )}
      </button>

      {/* Progress bar (for md and lg sizes) */}
      {size !== 'sm' && (
        <div className="flex-1 min-w-0">
          <div
            ref={progressRef}
            onClick={handleProgressClick}
            className="w-full h-2 bg-gray-200 rounded-full cursor-pointer"
          >
            <div
              className="h-full bg-blue-600 rounded-full transition-all"
              style={{
                width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%'
              }}
            />
          </div>
          
          {/* Time display */}
          {showDuration && (
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          )}
        </div>
      )}

      {/* Volume control (for lg size only) */}
      {size === 'lg' && (
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMute}
            className="text-gray-600 hover:text-gray-800"
            title={isMuted ? 'Включить звук' : 'Выключить звук'}
          >
            {isMuted ? <FiVolumeX size={16} /> : <FiVolume2 size={16} />}
          </button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      )}
    </div>
  );
};

export default AudioPlayer;
