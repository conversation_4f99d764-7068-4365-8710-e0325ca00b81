import apiClient from './api';
import { mockDataService } from './mockData';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = false;

// Audio file validation helper
const validateAudioFile = (file) => {
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/m4a'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Неподдерживаемый формат аудио файла. Поддерживаются: MP3, WAV, OGG, M4A');
  }

  if (file.size > maxSize) {
    throw new Error('Размер аудио файла не должен превышать 10MB');
  }

  return true;
};

export const contentService = {
  // Words management
  words: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getWords();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/word?${queryParams}`);
        return response.data.words;
      } catch (error) {
        toast.error('Ошибка загрузки слов');
        throw error;
      }
    },

    async getById(wordId) {
      try {
        if (USE_MOCK) {
          const words = await mockDataService.getWords();
          return words.find(word => word.id === wordId);
        }

        const response = await apiClient.get(`/word/${wordId}`);
        return response.data.word;
      } catch (error) {
        toast.error('Ошибка загрузки слова');
        throw error;
      }
    },

    async create(wordData, audioFile = null) {
      try {
        if (USE_MOCK) {
          // Mock creation
          const newWord = {
            id: Date.now(),
            ...wordData,
            created_at: new Date().toISOString()
          };
          toast.success('Слово создано успешно');
          return newWord;
        }

        // Debug logging
        console.log('🎵 Creating word with data:', wordData);
        console.log('🎵 Audio file:', audioFile);
        console.log('🎵 Audio file type:', audioFile?.type);
        console.log('🎵 Audio file size:', audioFile?.size);
        console.log('🎵 Audio file name:', audioFile?.name);

        // Always use the with-audio endpoint
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
          console.log('🎵 Audio file validation passed');
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', wordData.kaz_plaintext);
        formData.append('rus_plaintext', wordData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
          console.log('🎵 Audio file added to FormData');
        }

        // Debug FormData contents
        console.log('🎵 FormData contents:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}:`, value);
        }

        const response = await apiClient.post('/word/with-audio', formData);

        console.log('🎵 Server response:', response.data);
        toast.success('Слово создано успешно');
        return response.data.word;
      } catch (error) {
        console.error('🎵 Error creating word:', error);
        toast.error('Ошибка создания слова');
        throw error;
      }
    },

    async update(wordId, wordData, audioFile = null) {
      try {
        if (USE_MOCK) {
          // Mock update
          const updatedWord = {
            id: wordId,
            ...wordData,
            updated_at: new Date().toISOString()
          };
          toast.success('Слово обновлено успешно');
          return updatedWord;
        }

        // Always use the with-audio endpoint for updates too
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', wordData.kaz_plaintext);
        formData.append('rus_plaintext', wordData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
        }

        const response = await apiClient.put(`/word/with-audio/${wordId}`, formData);

        toast.success('Слово обновлено успешно');
        return response.data.word;
      } catch (error) {
        toast.error('Ошибка обновления слова');
        throw error;
      }
    },

    async delete(wordId) {
      try {
        if (USE_MOCK) {
          // Mock deletion
          toast.success('Слово удалено успешно');
          return { message: 'Word deleted successfully' };
        }

        const response = await apiClient.delete(`/word/${wordId}`);
        toast.success('Слово удалено успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления слова');
        throw error;
      }
    }
  },

  // Sentences management
  sentences: {
    async getAll(params = {}) {
      try {
        if (USE_MOCK) {
          return await mockDataService.getSentences();
        }

        const queryParams = new URLSearchParams(params);
        const response = await apiClient.get(`/sentence/all?${queryParams}`);
        return response.data.sentences;
      } catch (error) {
        toast.error('Ошибка загрузки предложений');
        throw error;
      }
    },

    async getById(sentenceId) {
      try {
        if (USE_MOCK) {
          const sentences = await mockDataService.getSentences();
          return sentences.find(sentence => sentence.id === sentenceId);
        }

        const response = await apiClient.get(`/sentence?id=${sentenceId}`);
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка загрузки предложения');
        throw error;
      }
    },

    async create(sentenceData, audioFile = null) {
      try {
        if (USE_MOCK) {
          const newSentence = {
            id: Date.now(),
            ...sentenceData,
            created_at: new Date().toISOString()
          };
          toast.success('Предложение создано успешно');
          return newSentence;
        }

        // Debug logging
        console.log('🎵 Creating sentence with data:', sentenceData);
        console.log('🎵 Audio file:', audioFile);

        // Always use the with-audio endpoint
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
          console.log('🎵 Audio file validation passed');
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', sentenceData.kaz_plaintext);
        formData.append('rus_plaintext', sentenceData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
          console.log('🎵 Audio file added to FormData');
        }

        // Debug FormData contents
        console.log('🎵 FormData contents:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}:`, value);
        }

        const response = await apiClient.post('/sentence/with-audio', formData);

        toast.success('Предложение создано успешно');
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка создания предложения');
        throw error;
      }
    },

    async update(sentenceId, sentenceData, audioFile = null) {
      try {
        if (USE_MOCK) {
          const updatedSentence = {
            id: sentenceId,
            ...sentenceData,
            updated_at: new Date().toISOString()
          };
          toast.success('Предложение обновлено успешно');
          return updatedSentence;
        }

        // Always use the with-audio endpoint for updates too
        if (audioFile) {
          // Validate audio file if provided
          validateAudioFile(audioFile);
        }

        const formData = new FormData();
        formData.append('kaz_plaintext', sentenceData.kaz_plaintext);
        formData.append('rus_plaintext', sentenceData.rus_plaintext);

        // Add audio file if provided (optional)
        if (audioFile) {
          formData.append('audio', audioFile);
        }

        const response = await apiClient.put(`/sentence/with-audio/${sentenceId}`, formData);

        toast.success('Предложение обновлено успешно');
        return response.data.sentence;
      } catch (error) {
        toast.error('Ошибка обновления предложения');
        throw error;
      }
    },

    async delete(sentenceId) {
      try {
        if (USE_MOCK) {
          toast.success('Предложение удалено успешно');
          return { message: 'Sentence deleted successfully' };
        }

        const response = await apiClient.delete(`/sentence/${sentenceId}`);
        toast.success('Предложение удалено успешно');
        return response.data;
      } catch (error) {
        toast.error('Ошибка удаления предложения');
        throw error;
      }
    }
  },

  // Questions management
  questions: {
    async getAll(params = {}) {
      try {
        // Always use mock for questions
        return await mockDataService.getQuestions();
      } catch (error) {
        toast.error('Ошибка загрузки вопросов');
        throw error;
      }
    },

    async getById(questionId) {
      try {
        // Always use mock for questions
        const questions = await mockDataService.getQuestions();
        return questions.find(question => question.id === questionId);
      } catch (error) {
        toast.error('Ошибка загрузки вопроса');
        throw error;
      }
    },

    async create(questionData) {
      try {
        // Always use mock for questions
        const newQuestion = {
          id: Date.now(),
          ...questionData,
          created_at: new Date().toISOString()
        };
        toast.success('Вопрос создан успешно');
        return newQuestion;
      } catch (error) {
        toast.error('Ошибка создания вопроса');
        throw error;
      }
    },

    async update(questionId, questionData) {
      try {
        // Always use mock for questions
        const updatedQuestion = {
          id: questionId,
          ...questionData,
          updated_at: new Date().toISOString()
        };
        toast.success('Вопрос обновлен успешно');
        return updatedQuestion;
      } catch (error) {
        toast.error('Ошибка обновления вопроса');
        throw error;
      }
    },

    async delete(questionId) {
      try {
        // Always use mock for questions
        toast.success('Вопрос удален успешно');
        return { message: 'Question deleted successfully' };
      } catch (error) {
        toast.error('Ошибка удаления вопроса');
        throw error;
      }
    }
  },

  // Theories management
  theories: {
    async getAll() {
      try {
        // Always use mock for theories
        return await mockDataService.getTheories();
      } catch (error) {
        toast.error('Ошибка загрузки теорий');
        throw error;
      }
    },

    async getById(theoryId) {
      try {
        // Always use mock for theories
        const theories = await mockDataService.getTheories();
        return theories.find(theory => theory.id === theoryId);
      } catch (error) {
        toast.error('Ошибка загрузки теории');
        throw error;
      }
    },

    async create(theoryData) {
      try {
        // Always use mock for theories
        const newTheory = {
          id: Date.now(),
          ...theoryData,
          created_at: new Date().toISOString()
        };
        toast.success('Теория создана успешно');
        return newTheory;
      } catch (error) {
        toast.error('Ошибка создания теории');
        throw error;
      }
    },

    async update(theoryId, theoryData) {
      try {
        // Always use mock for theories
        const updatedTheory = {
          id: theoryId,
          ...theoryData,
          updated_at: new Date().toISOString()
        };
        toast.success('Теория обновлена успешно');
        return updatedTheory;
      } catch (error) {
        toast.error('Ошибка обновления теории');
        throw error;
      }
    },

    async delete() {
      try {
        // Always use mock for theories
        toast.success('Теория удалена успешно');
        return { message: 'Theory deleted successfully' };
      } catch (error) {
        toast.error('Ошибка удаления теории');
        throw error;
      }
    }
  },

  // Modules management
  modules: {
    async getAll() {
      try {
        // Always use mock for modules
        return await mockDataService.getModules();
      } catch (error) {
        toast.error('Ошибка загрузки модулей');
        throw error;
      }
    },

    async getById(moduleId) {
      try {
        // Always use mock for modules
        const modules = await mockDataService.getModules();
        return modules.find(module => module.id === moduleId);
      } catch (error) {
        toast.error('Ошибка загрузки модуля');
        throw error;
      }
    },

    async create(moduleData) {
      try {
        // Always use mock for modules
        const newModule = {
          id: Date.now(),
          ...moduleData,
          created_at: new Date().toISOString()
        };
        toast.success('Модуль создан успешно');
        return newModule;
      } catch (error) {
        toast.error('Ошибка создания модуля');
        throw error;
      }
    },

    async update(moduleId, moduleData) {
      try {
        // Always use mock for modules
        const updatedModule = {
          id: moduleId,
          ...moduleData,
          updated_at: new Date().toISOString()
        };
        toast.success('Модуль обновлен успешно');
        return updatedModule;
      } catch (error) {
        toast.error('Ошибка обновления модуля');
        throw error;
      }
    },

    async delete() {
      try {
        // Always use mock for modules
        toast.success('Модуль удален успешно');
        return { message: 'Module deleted successfully' };
      } catch (error) {
        toast.error('Ошибка удаления модуля');
        throw error;
      }
    },

    // Get user progress for a module
    async getUserProgress(userId) {
      try {
        // Always use mock for modules
        return await mockDataService.getUserProgress(userId);
      } catch (error) {
        toast.error('Ошибка загрузки прогресса пользователя');
        throw error;
      }
    }
  }
};

// Export validation function for use in components
export { validateAudioFile };

export default contentService;
