import apiClient from './api';
import { mockDataService } from './mockData';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

export const systemService = {
  // Health check
  async getHealthCheck() {
    try {
      if (USE_MOCK) {
        return await mockDataService.getSystemHealth();
      }

      const response = await apiClient.get('/healthcheck');
      return response.data;
    } catch (error) {
      toast.error('Ошибка проверки состояния системы');
      throw error;
    }
  },

  // System metrics
  async getSystemMetrics() {
    try {
      if (USE_MOCK) {
        return {
          cmdline: ["./klingo-admin"],
          memstats: {
            Alloc: Math.floor(Math.random() * 10000000) + 1000000,
            TotalAlloc: Math.floor(Math.random() * 50000000) + 10000000,
            Sys: Math.floor(Math.random() * 20000000) + 5000000,
            NumGC: Math.floor(Math.random() * 100) + 20
          },
          total_requests_received: Math.floor(Math.random() * 10000) + 1000,
          total_responses_sent: Math.floor(Math.random() * 10000) + 1000,
          total_processing_time_μs: Math.floor(Math.random() * 100000000) + 10000000,
          total_responses_sent_by_status: {
            "200": Math.floor(Math.random() * 8000) + 800,
            "400": Math.floor(Math.random() * 100) + 50,
            "401": Math.floor(Math.random() * 50) + 30,
            "404": Math.floor(Math.random() * 30) + 20,
            "500": Math.floor(Math.random() * 20) + 10
          }
        };
      }

      const response = await apiClient.get('/debug/vars');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки системных метрик');
      throw error;
    }
  },

  // Overview statistics
  async getOverviewStats() {
    try {
      if (USE_MOCK) {
        return await mockDataService.getOverviewStats();
      }

      const response = await apiClient.get('/admin/stats/overview');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки общей статистики');
      throw error;
    }
  },

  // Module statistics
  async getModuleStats() {
    try {
      if (USE_MOCK) {
        return {
          totalModules: 24,
          activeModules: 22,
          completionRate: 78.5,
          averageTime: "00:15:30"
        };
      }

      const response = await apiClient.get('/admin/stats/modules');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки статистики модулей');
      throw error;
    }
  },

  // System settings
  async getSystemSettings() {
    try {
      if (USE_MOCK) {
        return await mockDataService.getSystemSettings();
      }

      const response = await apiClient.get('/admin/system/settings');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки системных настроек');
      throw error;
    }
  },

  // Update system settings
  async updateSystemSettings(settings) {
    try {
      if (USE_MOCK) {
        toast.success('Настройки сохранены успешно');
        return { message: 'Settings updated successfully', settings };
      }

      const response = await apiClient.put('/admin/system/settings', settings);
      toast.success('Настройки сохранены успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка сохранения настроек');
      throw error;
    }
  },

  // System Monitoring
  async getMonitorData() {
    try {
      if (USE_MOCK) {
        return await mockDataService.getSystemMonitor();
      }

      const response = await apiClient.get('/admin/system/monitor');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки данных мониторинга');
      throw error;
    }
  },

  // System Logs
  async getLogs(params = {}) {
    try {
      if (USE_MOCK) {
        return await mockDataService.getSystemLogs();
      }

      const response = await apiClient.get('/admin/system/logs', { params });
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки системных логов');
      throw error;
    }
  },

  async clearLogs() {
    try {
      if (USE_MOCK) {
        toast.success('Логи очищены успешно');
        return { message: 'Logs cleared successfully' };
      }

      const response = await apiClient.delete('/admin/system/logs');
      toast.success('Логи очищены успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка очистки логов');
      throw error;
    }
  },

  async exportLogs(format = 'json') {
    try {
      if (USE_MOCK) {
        toast.success('Экспорт логов начат (mock)');
        return { message: 'Export started' };
      }

      const response = await apiClient.get('/admin/system/logs/export', {
        params: { format },
        responseType: 'blob'
      });

      const blob = new Blob([response.data], {
        type: format === 'csv' ? 'text/csv' : 'application/json'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `system-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Логи экспортированы успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка экспорта логов');
      throw error;
    }
  },

  // Backup and Restore
  async createBackup() {
    try {
      if (USE_MOCK) {
        toast.success('Резервная копия создана (mock)');
        return { message: 'Backup created successfully' };
      }

      const response = await apiClient.post('/admin/system/backup', {}, {
        responseType: 'blob'
      });

      const blob = new Blob([response.data], { type: 'application/zip' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Резервная копия создана успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка создания резервной копии');
      throw error;
    }
  },

  async restoreBackup(backupFile) {
    try {
      if (USE_MOCK) {
        toast.success('Восстановление из резервной копии завершено (mock)');
        return { message: 'Restore completed successfully' };
      }

      const formData = new FormData();
      formData.append('backup', backupFile);

      const response = await apiClient.post('/admin/system/restore', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      toast.success('Восстановление из резервной копии завершено');
      return response.data;
    } catch (error) {
      toast.error('Ошибка восстановления из резервной копии');
      throw error;
    }
  }
};

export default systemService;
