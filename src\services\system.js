import apiClient from './api';

export const systemService = {
  // Health check
  async getHealthCheck() {
    const response = await apiClient.get('/healthcheck');
    return response.data;
  },

  // System metrics
  async getSystemMetrics() {
    const response = await apiClient.get('/debug/vars');
    return response.data;
  },

  // Overview statistics (placeholder - needs backend implementation)
  async getOverviewStats() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/stats/overview');
    return response.data;
  },

  // Module statistics (placeholder - needs backend implementation)
  async getModuleStats() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/stats/modules');
    return response.data;
  },

  // System settings (placeholder - needs backend implementation)
  async getSystemSettings() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/settings');
    return response.data;
  },

  // Update system settings (placeholder - needs backend implementation)
  async updateSystemSettings(settings) {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.put('/admin/settings', settings);
    return response.data;
  },

  // System Settings
  async getSettings() {
    const response = await apiClient.get('/system/settings');
    return response.data;
  },

  async saveSettings(settings) {
    const response = await apiClient.put('/system/settings', settings);
    return response.data;
  },

  // System Monitoring
  async getMonitorData() {
    const response = await apiClient.get('/system/monitor');
    return response.data;
  },

  // System Logs
  async getLogs(params = {}) {
    const response = await apiClient.get('/system/logs', { params });
    return response.data;
  },

  async clearLogs() {
    const response = await apiClient.delete('/system/logs');
    return response.data;
  },

  async exportLogs(format = 'json') {
    const response = await apiClient.get('/system/logs/export', {
      params: { format },
      responseType: 'blob'
    });

    const blob = new Blob([response.data], {
      type: format === 'csv' ? 'text/csv' : 'application/json'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return response.data;
  },

  // Backup and Restore
  async createBackup() {
    const response = await apiClient.post('/system/backup', {}, {
      responseType: 'blob'
    });

    const blob = new Blob([response.data], { type: 'application/zip' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `backup-${new Date().toISOString().split('T')[0]}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return response.data;
  }
};

export default systemService;
