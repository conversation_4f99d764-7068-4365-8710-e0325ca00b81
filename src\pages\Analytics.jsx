import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  FiUsers,
  FiFileText,
  FiServer,
  FiDownload,
  FiRefreshCw,
  FiCalendar,
  FiFilter
} from 'react-icons/fi';
import UserAnalytics from '../components/analytics/UserAnalytics';
import ContentAnalytics from '../components/analytics/ContentAnalytics';
import SystemAnalytics from '../components/analytics/SystemAnalytics';
import { mockDataService } from '../services/mockData';

// Use mock service in development mode
const USE_MOCK = true;

const Analytics = () => {
  const [activeTab, setActiveTab] = useState('users');
  const [dateRange, setDateRange] = useState('30d');

  // Fetch analytics data
  const { data: analyticsData, isLoading, error, refetch } = useQuery({
    queryKey: ['analytics', activeTab, dateRange],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getAnalytics(activeTab, dateRange);
      } else {
        // return await analyticsService.getAnalytics(activeTab, dateRange);
        return await mockDataService.getAnalytics(activeTab, dateRange);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const tabs = [
    { id: 'users', label: 'Пользователи', icon: FiUsers },
    { id: 'content', label: 'Контент', icon: FiFileText },
    { id: 'system', label: 'Система', icon: FiServer },
  ];

  const dateRanges = [
    { value: '7d', label: '7 дней' },
    { value: '30d', label: '30 дней' },
    { value: '90d', label: '90 дней' },
    { value: '1y', label: '1 год' },
  ];

  const handleExport = async () => {
    try {
      if (USE_MOCK) {
        // Mock export
        const data = JSON.stringify(analyticsData, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${activeTab}-${dateRange}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Загрузка аналитики...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-600 mb-2">Ошибка загрузки данных</div>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Попробовать снова
            </button>
          </div>
        </div>
      );
    }

    switch (activeTab) {
      case 'users':
        return <UserAnalytics data={analyticsData || {}} />;
      case 'content':
        return <ContentAnalytics data={analyticsData || {}} />;
      case 'system':
        return <SystemAnalytics data={analyticsData || {}} />;
      default:
        return <UserAnalytics data={analyticsData || {}} />;
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Аналитика</h1>
          <p className="text-gray-600 mt-1">
            Детальная статистика и метрики системы
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => refetch()}
            className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiRefreshCw size={16} />
            Обновить
          </button>
          <button
            onClick={handleExport}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <FiDownload size={16} />
            Экспорт
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center justify-between">
          {/* Tabs */}
          <div className="flex space-x-1">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Date Range Selector */}
          <div className="flex items-center gap-2">
            <FiCalendar className="text-gray-400" size={16} />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {dateRanges.map(range => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div>
        {renderContent()}
      </div>
    </div>
  );
};

export default Analytics;
