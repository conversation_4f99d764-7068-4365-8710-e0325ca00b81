import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, FiEdit2, <PERSON><PERSON>rash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ch, <PERSON><PERSON><PERSON><PERSON>, <PERSON>P<PERSON>age, <PERSON><PERSON><PERSON><PERSON>, FiTarget } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import ModuleModal from '../components/ModuleModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, formatDate } from '../utils/helpers';
import { MODULE_LEVELS } from '../utils/constants';

// Use mock service in development mode
const USE_MOCK = true;

// Extended mock modules data
const mockModules = [
  {
    id: 1,
    name: 'Базовые приветствия',
    level: MODULE_LEVELS.BEGINNER,
    theory_ids: [1],
    question_ids: [1, 2],
    pre_requisite_ids: [],
    created_at: '2024-01-01T12:00:00Z',
    users_count: 234,
    completion_rate: 85.5
  },
  {
    id: 2,
    name: 'Семья и родственники',
    level: MODULE_LEVELS.ELEMENTARY,
    theory_ids: [2],
    question_ids: [3],
    pre_requisite_ids: [1],
    created_at: '2024-01-02T12:00:00Z',
    users_count: 189,
    completion_rate: 78.2
  },
  {
    id: 3,
    name: 'Еда и напитки',
    level: MODULE_LEVELS.ELEMENTARY,
    theory_ids: [3],
    question_ids: [],
    pre_requisite_ids: [1],
    created_at: '2024-01-03T12:00:00Z',
    users_count: 156,
    completion_rate: 92.1
  },
  {
    id: 4,
    name: 'Время и даты',
    level: MODULE_LEVELS.INTERMEDIATE,
    theory_ids: [],
    question_ids: [],
    pre_requisite_ids: [1, 2],
    created_at: '2024-01-04T12:00:00Z',
    users_count: 98,
    completion_rate: 67.3
  },
];

const Modules = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('');
  const [selectedModule, setSelectedModule] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch modules
  const { data: modules = [], isLoading, error, refetch } = useQuery({
    queryKey: ['modules', searchTerm, levelFilter],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredModules = [...mockModules];
        
        if (searchTerm) {
          filteredModules = filteredModules.filter(module => 
            module.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (levelFilter) {
          filteredModules = filteredModules.filter(module => 
            module.level === parseInt(levelFilter)
          );
        }
        
        return filteredModules;
      } else {
        return await contentService.modules.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch related data for modal
  const { data: theories = [] } = useQuery({
    queryKey: ['theories'],
    queryFn: async () => {
      if (USE_MOCK) {
        return [
          { id: 1, name: 'Базовые приветствия', content: 'Теория о приветствиях...' },
          { id: 2, name: 'Семья и родственники', content: 'Теория о семье...' },
          { id: 3, name: 'Еда и напитки', content: 'Теория о еде...' },
        ];
      } else {
        return await contentService.theories.getAll();
      }
    },
    staleTime: 5 * 60 * 1000,
  });

  const { data: questions = [] } = useQuery({
    queryKey: ['questions'],
    queryFn: async () => {
      if (USE_MOCK) {
        return [
          { id: 1, question_text: 'Как сказать "привет"?', correct_answer: 'сәлем' },
          { id: 2, question_text: 'Что означает "рахмет"?', correct_answer: 'спасибо' },
          { id: 3, question_text: 'Переведите "мама"', correct_answer: 'ана' },
        ];
      } else {
        return await contentService.questions.getAll();
      }
    },
    staleTime: 5 * 60 * 1000,
  });

  const { data: words = [] } = useQuery({
    queryKey: ['words'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getWords();
      } else {
        return await contentService.words.getAll();
      }
    },
    staleTime: 5 * 60 * 1000,
  });

  // Create module mutation
  const createModuleMutation = useMutation({
    mutationFn: async (moduleData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { 
          id: Date.now(), 
          ...moduleData, 
          created_at: new Date().toISOString(),
          users_count: 0,
          completion_rate: 0
        };
      } else {
        return await contentService.modules.create(moduleData);
      }
    },
    onSuccess: () => {
      toast.success('Модуль успешно создан');
      queryClient.invalidateQueries(['modules']);
      setIsModalOpen(false);
      setSelectedModule(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании модуля');
      console.error('Create module error:', error);
    }
  });

  // Update module mutation
  const updateModuleMutation = useMutation({
    mutationFn: async ({ id, moduleData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...moduleData };
      } else {
        return await contentService.modules.update(id, moduleData);
      }
    },
    onSuccess: () => {
      toast.success('Модуль успешно обновлен');
      queryClient.invalidateQueries(['modules']);
      setIsModalOpen(false);
      setSelectedModule(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении модуля');
      console.error('Update module error:', error);
    }
  });

  // Delete module mutation
  const deleteModuleMutation = useMutation({
    mutationFn: async (moduleId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.modules.delete(moduleId);
      }
    },
    onSuccess: () => {
      toast.success('Модуль успешно удален');
      queryClient.invalidateQueries(['modules']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении модуля');
      console.error('Delete module error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateModule = () => {
    setSelectedModule(null);
    setIsModalOpen(true);
  };

  const handleEditModule = (module) => {
    setSelectedModule(module);
    setIsModalOpen(true);
  };

  const handleDeleteModule = (module) => {
    if (window.confirm(`Вы уверены, что хотите удалить модуль "${module.name}"?`)) {
      deleteModuleMutation.mutate(module.id);
    }
  };

  const handleSaveModule = (moduleData) => {
    if (selectedModule) {
      updateModuleMutation.mutate({ id: selectedModule.id, moduleData });
    } else {
      createModuleMutation.mutate(moduleData);
    }
  };

  const getLevelName = (level) => {
    const levels = {
      [MODULE_LEVELS.BEGINNER]: 'Начинающий',
      [MODULE_LEVELS.ELEMENTARY]: 'Элементарный',
      [MODULE_LEVELS.INTERMEDIATE]: 'Средний',
      [MODULE_LEVELS.ADVANCED]: 'Продвинутый',
      [MODULE_LEVELS.EXPERT]: 'Экспертный',
    };
    return levels[level] || `Уровень ${level}`;
  };

  const getLevelColor = (level) => {
    const colors = {
      [MODULE_LEVELS.BEGINNER]: 'bg-green-100 text-green-800',
      [MODULE_LEVELS.ELEMENTARY]: 'bg-blue-100 text-blue-800',
      [MODULE_LEVELS.INTERMEDIATE]: 'bg-yellow-100 text-yellow-800',
      [MODULE_LEVELS.ADVANCED]: 'bg-orange-100 text-orange-800',
      [MODULE_LEVELS.EXPERT]: 'bg-red-100 text-red-800',
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getPrerequisiteNames = (prerequisiteIds) => {
    if (!prerequisiteIds || prerequisiteIds.length === 0) return 'Нет';
    
    const names = prerequisiteIds
      .map(id => modules.find(module => module.id === id)?.name)
      .filter(Boolean);
    
    return names.length > 0 ? names.join(', ') : 'Неизвестные модули';
  };

  // Table columns
  const columns = [
    {
      header: 'Модуль',
      accessor: 'name',
      render: (row) => (
        <div>
          <div className="font-medium text-gray-900">{row.name}</div>
          <div className="text-sm text-gray-500">{formatDate(row.created_at)}</div>
        </div>
      )
    },
    {
      header: 'Уровень',
      accessor: 'level',
      render: (row) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(row.level)}`}>
          {getLevelName(row.level)}
        </span>
      )
    },
    {
      header: 'Содержание',
      accessor: 'content',
      render: (row) => (
        <div className="text-sm text-gray-600">
          <div>Теории: {row.theory_ids?.length || 0}</div>
          <div>Вопросы: {row.question_ids?.length || 0}</div>
        </div>
      )
    },
    {
      header: 'Требования',
      accessor: 'pre_requisite_ids',
      render: (row) => (
        <div className="text-sm text-gray-600">
          {getPrerequisiteNames(row.pre_requisite_ids)}
        </div>
      )
    },
    {
      header: 'Статистика',
      accessor: 'stats',
      render: (row) => (
        <div className="text-sm">
          <div className="flex items-center gap-1 text-gray-600">
            <FiUsers size={14} />
            <span>{row.users_count || 0} пользователей</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <FiTarget size={14} />
            <span>{row.completion_rate || 0}% завершений</span>
          </div>
        </div>
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEditModule(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteModule(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки модулей</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Модули</h1>
          <p className="text-gray-600 mt-1">
            Всего модулей: {modules.length}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleCreateModule}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiPlus size={16} />
            Создать модуль
          </button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по названию модуля..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Все уровни</option>
              {Object.values(MODULE_LEVELS).map(level => (
                <option key={level} value={level}>
                  {getLevelName(level)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Modules Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка модулей...</span>
          </div>
        ) : (
          <Table 
            columns={columns} 
            data={modules}
          />
        )}
      </div>

      {/* Module Modal */}
      <ModuleModal
        module={selectedModule}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedModule(null);
        }}
        onSave={handleSaveModule}
        isLoading={createModuleMutation.isLoading || updateModuleMutation.isLoading}
        modules={modules}
        theories={theories}
        questions={questions}
        words={words}
      />
    </div>
  );
};

export default Modules;
