# AudioPlayer Component Usage Guide

## 🎵 Обзор

Компонент `AudioPlayer` предназначен для воспроизведения аудио файлов из MinIO через API endpoint `/v1/files/get`. Автоматически конвертирует MinIO URLs в playable URLs.

## 🔧 API Integration

### Используемый endpoint:
```
GET /v1/files/get?bucket={bucket}&filename={filename}
```

### Автоматическая конвертация URL:
```javascript
// MinIO URL (из базы данных):
"http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3"

// Конвертируется в API URL:
"http://localhost:8080/v1/files/get?bucket=klingo-audio&filename=334c81d6-e27d-4bc3-bdfe-d58580def252.mp3"
```

## 💻 Использование компонента

### Базовое использование:
```jsx
import AudioPlayer from '../components/AudioPlayer';

<AudioPlayer 
  audioUrl="http://minio:9000/klingo-audio/file.mp3"
  size="sm"
/>
```

### Все доступные props:
```jsx
<AudioPlayer 
  audioUrl="http://minio:9000/klingo-audio/file.mp3"  // MinIO URL
  size="sm"                    // "sm" | "md" | "lg"
  showDuration={false}         // Показать время
  autoPlay={false}             // Автовоспроизведение
  className="custom-class"     // Дополнительные CSS классы
  onPlay={() => console.log('Started')}    // Callback при старте
  onPause={() => console.log('Paused')}    // Callback при паузе
  onEnded={() => console.log('Ended')}     // Callback при окончании
/>
```

## 🎛️ Размеры компонента

### Small (sm) - для таблиц:
- Только кнопка play/pause
- Размер: 32x32px
- Без прогресс-бара

### Medium (md) - для карточек:
- Кнопка play/pause + прогресс-бар
- Размер кнопки: 40x40px
- С прогресс-баром

### Large (lg) - для детальных страниц:
- Кнопка play/pause + прогресс-бар + контроль громкости
- Размер кнопки: 48x48px
- С временем воспроизведения

## 📋 Примеры использования

### В таблице (Words.jsx, Sentences.jsx):
```jsx
{
  header: 'Аудио',
  accessor: 'audio_url',
  render: (row) => (
    <AudioPlayer 
      audioUrl={row.audio_url}
      size="sm"
      onPlay={() => console.log('Playing:', row.kaz_plaintext)}
    />
  )
}
```

### В модале редактирования:
```jsx
<AudioPlayer 
  audioUrl={word.audio_url}
  size="md"
  showDuration={true}
  className="mb-4"
/>
```

### На странице детального просмотра:
```jsx
<AudioPlayer 
  audioUrl={sentence.audio_url}
  size="lg"
  showDuration={true}
  onPlay={() => trackPlayEvent(sentence.id)}
  onEnded={() => markAsListened(sentence.id)}
/>
```

## 🔄 Обработка состояний

### Состояния компонента:
- **Загрузка**: Показывает спиннер
- **Готов**: Показывает кнопку play
- **Воспроизведение**: Показывает кнопку pause
- **Ошибка**: Показывает сообщение об ошибке
- **Нет аудио**: Показывает "Нет аудио"

### Автоматическая обработка:
- ✅ Конвертация MinIO URL в API URL
- ✅ Обработка ошибок загрузки
- ✅ Показ прогресса воспроизведения
- ✅ Контроль громкости (для lg размера)
- ✅ Cleanup при размонтировании

## 🛠️ Вспомогательные функции

### В filesService:
```javascript
// Конвертация MinIO URL в playable URL
const playableUrl = filesService.getPlayableUrl(minioUrl);

// Парсинг MinIO URL
const { bucket, filename } = filesService.parseMinioUrl(minioUrl);

// Получение файла через API
const blobUrl = await filesService.getFile(bucket, filename);
```

## 🎯 Интеграция с существующими компонентами

### Обновленные компоненты:
- ✅ **Words.jsx** - использует AudioPlayer в таблице
- ✅ **Sentences.jsx** - использует AudioPlayer в таблице
- 🔄 **WordModal.jsx** - можно добавить для предпросмотра
- 🔄 **SentenceModal.jsx** - можно добавить для предпросмотра

### Удаленный код:
- ❌ Старые функции `handlePlayAudio`
- ❌ State `playingAudio` и `audioElements`
- ❌ Ручное управление Audio элементами

## 🚀 Преимущества нового подхода

1. **Единообразие**: Один компонент для всех случаев
2. **Автоматическая конвертация**: MinIO URLs → API URLs
3. **Лучший UX**: Прогресс-бар, контроль громкости
4. **Обработка ошибок**: Автоматическая обработка сетевых ошибок
5. **Производительность**: Правильный cleanup ресурсов
6. **Гибкость**: Разные размеры для разных контекстов

## 🔧 Настройка API

Убедитесь, что endpoint `/v1/files/get` настроен правильно:

```bash
# Тестирование endpoint
curl -X GET "http://localhost:8080/v1/files/get?bucket=klingo-audio&filename=test.mp3"
```

Должен возвращать аудио файл с правильными заголовками:
- `Content-Type`: audio/mpeg (или соответствующий)
- `Content-Length`: размер файла
- `Cache-Control`: public, max-age=3600
- `Accept-Ranges`: bytes

Теперь аудио воспроизведение полностью интегрировано с бэкендом! 🎵
