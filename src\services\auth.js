import apiClient from './api';
import mockAuthService from './mockAuth';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = true;

// Token management constants
const TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';
const USER_KEY = 'user';

// Authentication service
export const authService = {
  // Login admin
  async login(credentials) {
    try {
      let response;

      if (USE_MOCK) {
        // Use mock service in development
        response = await mockAuthService.login(credentials);
      } else {
        // Use real API in production
        const apiResponse = await apiClient.post('/auth/login', credentials);
        // API returns data in 'response' field
        response = apiResponse.data.response || apiResponse.data;
      }

      const { tokens, user } = response;

      // Save tokens to localStorage
      localStorage.setItem(TOKEN_KEY, tokens.access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);
      localStorage.setItem(USER_KEY, JSON.stringify(user));

      return { tokens, user };
    } catch (error) {
      const message = error.response?.data?.error || 'Ошибка входа в систему';
      toast.error(message);
      throw error;
    }
  },

  // Register user (for admin registration)
  async register(userData) {
    try {
      let response;

      if (USE_MOCK) {
        // Use mock service in development
        response = await mockAuthService.register(userData);
      } else {
        // Use real API in production
        const apiResponse = await apiClient.post('/auth/register', userData);
        // API returns data in 'response' field
        response = apiResponse.data.response || apiResponse.data;
      }

      const { tokens, user } = response;

      // Save tokens to localStorage
      localStorage.setItem(TOKEN_KEY, tokens.access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);
      localStorage.setItem(USER_KEY, JSON.stringify(user));

      toast.success('Регистрация прошла успешно');
      return { tokens, user };
    } catch (error) {
      const message = error.response?.data?.error || 'Ошибка регистрации';
      toast.error(message);
      throw error;
    }
  },

  // Refresh access token
  async refreshToken() {
    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      let response;
      if (USE_MOCK) {
        // Mock refresh token logic
        response = await mockAuthService.refreshToken(refreshToken);
      } else {
        // Use real API
        const apiResponse = await apiClient.post('/auth/refresh', {
          refresh_token: refreshToken
        });
        // API returns data in 'response' field
        response = apiResponse.data.response || apiResponse.data;
      }

      const { tokens } = response;
      localStorage.setItem(TOKEN_KEY, tokens.access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);

      return tokens;
    } catch (error) {
      // If refresh fails, logout user
      this.logout();
      throw error;
    }
  },

  // Logout
  logout() {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    window.location.href = '/login';
  },

  // Get current user from localStorage
  getCurrentUser() {
    const userStr = localStorage.getItem(USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  },

  // Get access token
  getAccessToken() {
    return localStorage.getItem(TOKEN_KEY);
  },

  // Get refresh token
  getRefreshToken() {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  },

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getAccessToken();
    const user = this.getCurrentUser();
    return !!(token && user);
  },

  // Check if user is admin
  isAdmin() {
    const user = this.getCurrentUser();
    // Assuming admin role is stored in user object
    return user?.role === 'admin' || user?.is_admin === true;
  },

  // Verify token
  async verifyToken() {
    try {
      if (USE_MOCK) {
        // Use mock service in development
        return await mockAuthService.verifyToken();
      } else {
        // Use real API in production
        const response = await apiClient.get('/auth/verify');
        return response.data;
      }
    } catch (error) {
      // If verification fails, logout
      this.logout();
      throw error;
    }
  },

  // Update user profile
  async updateProfile(userData) {
    try {
      let response;

      if (USE_MOCK) {
        response = await mockAuthService.updateProfile(userData);
      } else {
        const apiResponse = await apiClient.put('/auth/profile', userData);
        response = apiResponse.data;
      }

      const { user } = response;
      localStorage.setItem(USER_KEY, JSON.stringify(user));

      toast.success('Профиль обновлен успешно');
      return user;
    } catch (error) {
      const message = error.response?.data?.error || 'Ошибка обновления профиля';
      toast.error(message);
      throw error;
    }
  },

  // Change password
  async changePassword(passwordData) {
    try {
      let response;

      if (USE_MOCK) {
        response = await mockAuthService.changePassword(passwordData);
      } else {
        const apiResponse = await apiClient.put('/auth/password', passwordData);
        response = apiResponse.data;
      }

      toast.success('Пароль изменен успешно');
      return response;
    } catch (error) {
      const message = error.response?.data?.error || 'Ошибка изменения пароля';
      toast.error(message);
      throw error;
    }
  }
};

export default authService;
