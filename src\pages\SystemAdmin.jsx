import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  FiSettings, 
  FiActivity, 
  FiFileText, 
  FiDatabase,
  FiShield,
  FiRefreshCw,
  FiDownload,
  FiUpload,
  FiTrash2
} from 'react-icons/fi';
import toast from 'react-hot-toast';
import SystemSettings from '../components/admin/SystemSettings';
import SystemMonitor from '../components/admin/SystemMonitor';
import SystemLogs from '../components/admin/SystemLogs';
import { mockDataService } from '../services/mockData';
import systemService from '../services/system';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

const SystemAdmin = () => {
  const [activeTab, setActiveTab] = useState('settings');

  const queryClient = useQueryClient();

  // Fetch system settings
  const { data: settings, isLoading: settingsLoading, refetch: refetchSettings } = useQuery({
    queryKey: ['system-settings'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getSystemSettings();
      } else {
        return await systemService.getSettings();
      }
    },
    staleTime: 5 * 60 * 1000,
    enabled: activeTab === 'settings'
  });

  // Fetch system monitor data
  const { data: monitorData, isLoading: monitorLoading, refetch: refetchMonitor } = useQuery({
    queryKey: ['system-monitor'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getSystemMonitor();
      } else {
        return await systemService.getMonitorData();
      }
    },
    staleTime: 30 * 1000,
    enabled: activeTab === 'monitor',
    refetchInterval: activeTab === 'monitor' ? 30000 : false
  });

  // Fetch system logs
  const { data: logs = [], isLoading: logsLoading, refetch: refetchLogs } = useQuery({
    queryKey: ['system-logs'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getSystemLogs();
      } else {
        return await systemService.getLogs();
      }
    },
    staleTime: 60 * 1000,
    enabled: activeTab === 'logs'
  });

  // Save settings mutation
  const saveSettingsMutation = useMutation({
    mutationFn: async (settingsData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return settingsData;
      } else {
        return await systemService.saveSettings(settingsData);
      }
    },
    onSuccess: () => {
      toast.success('Настройки успешно сохранены');
      queryClient.invalidateQueries(['system-settings']);
    },
    onError: (error) => {
      toast.error('Ошибка при сохранении настроек');
      console.error('Save settings error:', error);
    }
  });

  // Backup mutation
  const createBackupMutation = useMutation({
    mutationFn: async () => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        // Mock download
        const data = JSON.stringify({ backup: 'mock_data', timestamp: new Date().toISOString() }, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        return { success: true };
      } else {
        return await systemService.createBackup();
      }
    },
    onSuccess: () => {
      toast.success('Резервная копия создана');
    },
    onError: (error) => {
      toast.error('Ошибка при создании резервной копии');
      console.error('Backup error:', error);
    }
  });

  // Clear logs mutation
  const clearLogsMutation = useMutation({
    mutationFn: async () => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await systemService.clearLogs();
      }
    },
    onSuccess: () => {
      toast.success('Логи очищены');
      queryClient.invalidateQueries(['system-logs']);
    },
    onError: (error) => {
      toast.error('Ошибка при очистке логов');
      console.error('Clear logs error:', error);
    }
  });

  const tabs = [
    { id: 'settings', label: 'Настройки', icon: FiSettings },
    { id: 'monitor', label: 'Мониторинг', icon: FiActivity },
    { id: 'logs', label: 'Логи', icon: FiFileText },
    { id: 'backup', label: 'Резервное копирование', icon: FiDatabase },
  ];

  const handleSaveSettings = (settingsData) => {
    saveSettingsMutation.mutate(settingsData);
  };

  const handleExportLogs = async () => {
    try {
      if (USE_MOCK) {
        const data = JSON.stringify(logs, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `logs-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        toast.success('Логи экспортированы');
      } else {
        await systemService.exportLogs();
      }
    } catch (error) {
      toast.error('Ошибка при экспорте логов');
      console.error('Export logs error:', error);
    }
  };

  const handleCreateBackup = () => {
    if (window.confirm('Создать резервную копию системы? Это может занять несколько минут.')) {
      createBackupMutation.mutate();
    }
  };

  const handleClearLogs = () => {
    if (window.confirm('Вы уверены, что хотите очистить все логи? Это действие нельзя отменить.')) {
      clearLogsMutation.mutate();
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'settings':
        return (
          <SystemSettings
            settings={settings}
            onSave={handleSaveSettings}
            isLoading={saveSettingsMutation.isLoading}
          />
        );
      case 'monitor':
        return (
          <SystemMonitor
            data={monitorData || {}}
            onRefresh={refetchMonitor}
            isLoading={monitorLoading}
          />
        );
      case 'logs':
        return (
          <SystemLogs
            logs={logs}
            onRefresh={refetchLogs}
            onExport={handleExportLogs}
            isLoading={logsLoading}
          />
        );
      case 'backup':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Резервное копирование и восстановление
            </h3>
            
            <div className="space-y-6">
              {/* Create Backup */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Создать резервную копию</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Создайте полную резервную копию системы, включая базу данных, настройки и файлы.
                </p>
                <button
                  onClick={handleCreateBackup}
                  disabled={createBackupMutation.isLoading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  <FiDownload size={16} />
                  {createBackupMutation.isLoading ? 'Создание...' : 'Создать резервную копию'}
                </button>
              </div>

              {/* Restore Backup */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Восстановить из резервной копии</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Восстановите систему из ранее созданной резервной копии.
                </p>
                <div className="flex items-center gap-3">
                  <input
                    type="file"
                    accept=".json,.sql,.zip"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  <button
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    disabled
                  >
                    <FiUpload size={16} />
                    Восстановить
                  </button>
                </div>
                <p className="text-xs text-yellow-600 mt-2">
                  ⚠️ Восстановление заменит все текущие данные
                </p>
              </div>

              {/* Maintenance */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Обслуживание системы</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Инструменты для обслуживания и очистки системы.
                </p>
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleClearLogs}
                    disabled={clearLogsMutation.isLoading}
                    className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    <FiTrash2 size={16} />
                    {clearLogsMutation.isLoading ? 'Очистка...' : 'Очистить логи'}
                  </button>
                  <button
                    className="flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
                    disabled
                  >
                    <FiRefreshCw size={16} />
                    Оптимизировать БД
                  </button>
                </div>
              </div>

              {/* System Info */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Информация о системе</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Версия:</span> 1.0.0
                  </div>
                  <div>
                    <span className="font-medium">Последнее обновление:</span> 15.01.2024
                  </div>
                  <div>
                    <span className="font-medium">Время работы:</span> 15 дней 8 часов
                  </div>
                  <div>
                    <span className="font-medium">Последний бэкап:</span> 14.01.2024
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Системное администрирование</h1>
          <p className="text-gray-600 mt-1">
            Управление настройками, мониторинг и обслуживание системы
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => {
              if (activeTab === 'settings') refetchSettings();
              if (activeTab === 'monitor') refetchMonitor();
              if (activeTab === 'logs') refetchLogs();
            }}
            className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiRefreshCw size={16} />
            Обновить
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <div className="flex space-x-8 px-6">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div>
        {renderContent()}
      </div>
    </div>
  );
};

export default SystemAdmin;
