import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiAlertCircle } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import UserFilters from '../components/UserFilters';
import UserModal from '../components/UserModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import usersService from '../services/users';
import { formatDate, formatDateTime } from '../utils/helpers';
import { USER_STATUS, PAGINATION } from '../utils/constants';

// Use mock service in development mode
const USE_MOCK = true;

const Users = () => {
  const [filters, setFilters] = useState({
    page: PAGINATION.DEFAULT_PAGE,
    limit: PAGINATION.DEFAULT_LIMIT,
    search: '',
    status: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const queryClient = useQueryClient();

  // Fetch users
  const { 
    data: usersData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['users', filters],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getUsers(filters);
      } else {
        return await usersService.getUsers(filters);
      }
    },
    keepPreviousData: true,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Update user status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ userId, status, reason }) => {
      if (USE_MOCK) {
        // Mock implementation
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      } else {
        return await usersService.updateUserStatus(userId, status, reason);
      }
    },
    onSuccess: () => {
      toast.success('Статус пользователя обновлен');
      queryClient.invalidateQueries(['users']);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении статуса пользователя');
      console.error('Update status error:', error);
    }
  });

  // Table columns
  const columns = [
    {
      header: 'Пользователь',
      accessor: 'user',
      render: (row) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            {row.image_url ? (
              <img 
                src={row.image_url} 
                alt={`${row.name} ${row.surname}`}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <FiUsers size={16} className="text-blue-600" />
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">{row.name} {row.surname}</div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Дата регистрации',
      accessor: 'created_at',
      render: (row) => (
        <div>
          <div className="text-sm text-gray-900">{formatDate(row.created_at)}</div>
          <div className="text-xs text-gray-500">{formatDateTime(row.created_at)}</div>
        </div>
      )
    },
    {
      header: 'Последняя активность',
      accessor: 'last_activity',
      render: (row) => (
        <div>
          <div className="text-sm text-gray-900">{formatDate(row.last_activity)}</div>
          <div className="text-xs text-gray-500">{formatDateTime(row.last_activity)}</div>
        </div>
      )
    },
    {
      header: 'Прогресс',
      accessor: 'progress',
      render: (row) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${row.progress}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium">{row.progress}%</span>
        </div>
      )
    },
    {
      header: 'Статус',
      accessor: 'status',
      render: (row) => {
        const statusColors = {
          [USER_STATUS.ACTIVE]: 'bg-green-100 text-green-800',
          [USER_STATUS.BLOCKED]: 'bg-red-100 text-red-800',
        };
        
        const statusText = {
          [USER_STATUS.ACTIVE]: 'Активен',
          [USER_STATUS.BLOCKED]: 'Заблокирован',
        };

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[row.status] || 'bg-gray-100 text-gray-800'}`}>
            {statusText[row.status] || 'Неизвестно'}
          </span>
        );
      }
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <button
          onClick={() => handleViewUser(row)}
          className="flex items-center gap-2 px-3 py-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
        >
          <FiEye size={16} />
          Просмотр
        </button>
      )
    }
  ];

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleFilterReset = () => {
    setFilters({
      page: PAGINATION.DEFAULT_PAGE,
      limit: PAGINATION.DEFAULT_LIMIT,
      search: '',
      status: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleUpdateUserStatus = async (userId, status, reason) => {
    await updateStatusMutation.mutateAsync({ userId, status, reason });
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Loading state
  if (isLoading && !usersData) {
    return (
      <div className="flex items-center justify-center h-64">
        <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
        <span className="ml-2 text-gray-600">Загрузка пользователей...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiAlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <div className="text-red-600 mb-2">Ошибка загрузки пользователей</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  const users = usersData?.users || [];
  const totalUsers = usersData?.total || 0;
  const totalPages = Math.ceil(totalUsers / filters.limit);

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Пользователи</h1>
          <p className="text-gray-600 mt-1">
            Всего пользователей: {totalUsers}
          </p>
        </div>
      </div>

      {/* Filters */}
      <UserFilters
        filters={filters}
        onChange={handleFilterChange}
        onReset={handleFilterReset}
      />

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <Table 
          columns={columns} 
          data={users}
          loading={isLoading}
        />
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Показано {((filters.page - 1) * filters.limit) + 1} - {Math.min(filters.page * filters.limit, totalUsers)} из {totalUsers}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(filters.page - 1)}
                disabled={filters.page <= 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Назад
              </button>
              <span className="px-3 py-1 text-sm">
                {filters.page} из {totalPages}
              </span>
              <button
                onClick={() => handlePageChange(filters.page + 1)}
                disabled={filters.page >= totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Далее
              </button>
            </div>
          </div>
        )}
      </div>

      {/* User Modal */}
      <UserModal
        user={selectedUser}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedUser(null);
        }}
        onUpdateStatus={handleUpdateUserStatus}
      />
    </div>
  );
};

export default Users;
