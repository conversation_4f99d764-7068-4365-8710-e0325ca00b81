# План интеграции админ панели с бэкендом

## 📋 Текущее состояние

### ✅ Существующие API endpoints (готовые к использованию)

#### Аутентификация
- `POST /v1/auth/register` - Регистрация пользователя
- `POST /v1/auth/login` - Вход в систему

#### Управление контентом
- `POST /v1/word` - Создание слова
- `GET /v1/word` - Получение всех слов
- `POST /v1/sentence` - Создание предложения
- `GET /v1/sentence` - Получение предложения по ID
- `GET /v1/sentence/all` - Получение всех предложений
- `POST /v1/questions` - Создание вопроса
- `GET /v1/questions` - Получение вопроса по ID
- `GET /v1/questions/all` - Получение всех вопросов
- `POST /v1/theory` - Создание теории
- `GET /v1/theory` - Получение теории по ID
- `GET /v1/theory/all` - Получение всех теорий
- `PUT /v1/theory` - Обновление теории
- `DELETE /v1/theory` - Удаление теории
- `POST /v1/module` - Создание модуля
- `GET /v1/module` - Получение модуля по ID
- `GET /v1/module/all` - Получение всех модулей
- `PUT /v1/module` - Обновление модуля
- `DELETE /v1/module` - Удаление модуля

#### Файловая система
- `POST /v1/files/upload/audio` - Загрузка аудио файлов
- `POST /v1/files/upload/image` - Загрузка изображений
- `POST /v1/files/upload/multiple` - Множественная загрузка
- `DELETE /v1/files/delete` - Удаление файла
- `GET /v1/files/list` - Список файлов

#### Системные endpoints
- `GET /v1/healthcheck` - Проверка состояния системы
- `GET /debug/vars` - Метрики приложения

#### Прогресс и достижения
- `GET /v1/module-user-progress/{id}` - Прогресс пользователя
- `POST /v1/progress/save` - Сохранение прогресса
- `GET /v1/progress/streak/{id}` - Серия дней обучения
- `GET /v1/achievements/{id}` - Достижения пользователя
- `POST /v1/achievements` - Создание достижения

### ❌ Недостающие API endpoints (требуют реализации)

#### Административные endpoints для управления пользователями
```
GET /v1/admin/users - Получение списка пользователей с фильтрами
GET /v1/admin/users/{id} - Детальная информация о пользователе
PUT /v1/admin/users/{id}/status - Изменение статуса пользователя
DELETE /v1/admin/users/{id} - Удаление пользователя
GET /v1/admin/users/{id}/progress - Прогресс пользователя
GET /v1/admin/users/{id}/achievements - Достижения пользователя
```

#### Статистика и аналитика
```
GET /v1/admin/stats/overview - Общая статистика системы
GET /v1/admin/stats/users - Статистика пользователей
GET /v1/admin/stats/content - Статистика контента
GET /v1/admin/stats/modules - Статистика модулей
GET /v1/admin/stats/performance - Статистика производительности
```

#### Системное администрирование
```
GET /v1/admin/system/settings - Получение системных настроек
PUT /v1/admin/system/settings - Обновление системных настроек
GET /v1/admin/system/logs - Получение системных логов
POST /v1/admin/system/backup - Создание резервной копии
POST /v1/admin/system/restore - Восстановление из резервной копии
```

#### Недостающие CRUD операции
```
PUT /v1/word/{id} - Обновление слова
DELETE /v1/word/{id} - Удаление слова
PUT /v1/sentence/{id} - Обновление предложения
DELETE /v1/sentence/{id} - Удаление предложения
PUT /v1/questions/{id} - Обновление вопроса
DELETE /v1/questions/{id} - Удаление вопроса
```

## 🔧 Компоненты админ панели, использующие моковые данные

### Dashboard (src/pages/Dashboard.jsx)
- **Использует**: `mockDataService.getOverviewStats()`
- **Нужно**: `GET /v1/admin/stats/overview`
- **Данные**: общая статистика, активные пользователи, популярные модули

### Users (src/pages/Users.jsx)
- **Использует**: `mockDataService.getUsers()`
- **Нужно**: `GET /v1/admin/users` с поддержкой фильтров и пагинации
- **Данные**: список пользователей, статусы, даты регистрации

### Analytics (src/pages/Analytics.jsx)
- **Использует**: `mockDataService.getAnalytics()`
- **Нужно**: `GET /v1/admin/stats/{type}` для разных типов аналитики
- **Данные**: графики активности, статистика контента, производительность

### SystemAdmin (src/pages/SystemAdmin.jsx)
- **Использует**: `mockDataService.getSystemSettings()`, `getSystemMonitor()`, `getSystemLogs()`
- **Нужно**: соответствующие admin endpoints для настроек, мониторинга и логов

### Content Management Pages
- **Words, Sentences, Questions, Theories, Modules**: частично используют моки
- **Нужно**: дополнить недостающие CRUD операции

## 🚀 План интеграции по этапам

### Этап 1: Аутентификация и базовая настройка
1. Создать `authService` для работы с `/v1/auth/*`
2. Настроить обработку JWT токенов
3. Добавить refresh token logic
4. Обновить API interceptors

### Этап 2: Управление контентом
1. Обновить `contentService` для использования реальных API
2. Добавить недостающие CRUD операции
3. Интегрировать файловую систему
4. Обновить компоненты Words, Sentences, Questions, Theories, Modules

### Этап 3: Система пользователей
1. Создать административные endpoints для пользователей
2. Обновить `usersService`
3. Интегрировать с Users страницей

### Этап 4: Аналитика и статистика
1. Создать endpoints для статистики
2. Обновить Dashboard и Analytics страницы
3. Добавить real-time обновления

### Этап 5: Системное администрирование
1. Создать admin endpoints для системных настроек
2. Интегрировать мониторинг и логи
3. Добавить функции резервного копирования

## 📝 Детальные требования к недостающим endpoints

### GET /v1/admin/users
**Параметры запроса:**
```json
{
  "page": 1,
  "limit": 20,
  "search": "string (optional)",
  "status": "active|inactive|banned (optional)",
  "sortBy": "created_at|name|email",
  "sortOrder": "asc|desc"
}
```

**Ожидаемый ответ:**
```json
{
  "users": [
    {
      "id": 1,
      "name": "Айдар",
      "surname": "Нурланов", 
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2024-01-01T12:00:00Z",
      "last_login": "2024-01-15T10:30:00Z",
      "modules_completed": 5,
      "total_time_spent": "02:45:30"
    }
  ],
  "total": 1250,
  "page": 1,
  "limit": 20,
  "totalPages": 63
}
```

### GET /v1/admin/stats/overview
**Ожидаемый ответ:**
```json
{
  "total_users": 1250,
  "active_users_today": 89,
  "active_users_week": 456,
  "total_modules": 15,
  "total_questions": 450,
  "total_words": 2300,
  "total_sentences": 1890,
  "total_theories": 89,
  "avg_completion_rate": 67.5,
  "popular_modules": [
    {
      "id": 1,
      "name": "Базовые приветствия",
      "users_count": 234,
      "completion_rate": 85.2
    }
  ],
  "recent_activity": [
    {
      "user_id": 1,
      "user_name": "Айдар Нурланов",
      "action": "completed_module",
      "module_name": "Базовые приветствия",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### GET /v1/admin/system/settings
**Ожидаемый ответ:**
```json
{
  "general": {
    "siteName": "Klingo Admin",
    "siteDescription": "Административная панель для изучения казахского языка",
    "defaultLanguage": "ru",
    "timezone": "Asia/Almaty",
    "maintenanceMode": false
  },
  "email": {
    "smtpHost": "smtp.gmail.com",
    "smtpPort": 587,
    "smtpUser": "<EMAIL>",
    "fromEmail": "<EMAIL>",
    "fromName": "Klingo"
  },
  "security": {
    "sessionTimeout": 60,
    "maxLoginAttempts": 5,
    "passwordMinLength": 8,
    "requireTwoFactor": false,
    "allowRegistration": true
  },
  "storage": {
    "maxFileSize": 10,
    "allowedFileTypes": "jpg,jpeg,png,gif,mp3,wav,pdf",
    "autoCleanup": true,
    "cleanupDays": 30
  }
}
```

## ⚠️ Важные замечания

1. **Пагинация**: Текущие endpoints не поддерживают пагинацию. Рекомендуется добавить для всех списочных endpoints.

2. **Фильтрация и сортировка**: Большинство endpoints не поддерживают фильтрацию. Необходимо добавить для удобства администрирования.

3. **Валидация**: Убедиться, что все endpoints имеют правильную валидацию входных данных.

4. **Права доступа**: Добавить проверку административных прав для admin endpoints.

5. **Rate Limiting**: Учесть существующие ограничения (2 запроса в секунду).

6. **Error Handling**: Обеспечить консистентную обработку ошибок во всех endpoints.

## 🔄 Следующие шаги

1. Создать недостающие backend endpoints согласно спецификации
2. Обновить существующие сервисы в админ панели ✅
3. Протестировать интеграцию
4. Оптимизировать производительность
5. Добавить обработку ошибок и loading states

## ✅ Выполненные задачи

### Интеграция сервисов
- ✅ Обновлен `authService` с полной поддержкой JWT токенов и refresh logic
- ✅ Обновлен `apiClient` с автоматическим обновлением токенов
- ✅ Обновлен `contentService` для всех CRUD операций
- ✅ Обновлен `usersService` с поддержкой административных функций
- ✅ Создан `analyticsService` для статистики и аналитики
- ✅ Обновлен `systemService` для системного администрирования
- ✅ Обновлен `filesService` с поддержкой прогресса загрузки

### Функциональность
- ✅ Автоматическое обновление JWT токенов
- ✅ Обработка ошибок с пользовательскими уведомлениями
- ✅ Поддержка mock данных в development режиме
- ✅ Прогресс загрузки файлов
- ✅ Валидация файлов перед загрузкой

## 🚨 Критически важные endpoints для реализации

Эти endpoints необходимы для полноценной работы админ панели:

### 1. Административные пользователи (ВЫСОКИЙ ПРИОРИТЕТ)
```
GET /v1/admin/users
PUT /v1/admin/users/{id}/status
DELETE /v1/admin/users/{id}
```

### 2. Статистика для Dashboard (ВЫСОКИЙ ПРИОРИТЕТ)
```
GET /v1/admin/stats/overview
GET /v1/admin/stats/users
GET /v1/admin/stats/content
```

### 3. Системные настройки (СРЕДНИЙ ПРИОРИТЕТ)
```
GET /v1/admin/system/settings
PUT /v1/admin/system/settings
GET /v1/admin/system/monitor
GET /v1/admin/system/logs
```

### 4. Недостающие CRUD операции (СРЕДНИЙ ПРИОРИТЕТ)
```
PUT /v1/word/{id}
DELETE /v1/word/{id}
PUT /v1/sentence/{id}
DELETE /v1/sentence/{id}
PUT /v1/questions/{id}
DELETE /v1/questions/{id}
```

### 5. Refresh Token (ВЫСОКИЙ ПРИОРИТЕТ)
```
POST /v1/auth/refresh
```

## 📊 Статус интеграции по компонентам

| Компонент | Статус | Примечания |
|-----------|--------|------------|
| Аутентификация | ✅ Готово | Нужен только refresh endpoint |
| Управление словами | ✅ Готово | Нужны PUT/DELETE endpoints |
| Управление предложений | ✅ Готово | Нужны PUT/DELETE endpoints |
| Управление вопросов | ✅ Готово | Нужны PUT/DELETE endpoints |
| Управление теорий | ✅ Готово | Полностью интегрировано |
| Управление модулей | ✅ Готово | Полностью интегрировано |
| Управление пользователями | ⚠️ Частично | Нужны admin endpoints |
| Dashboard | ⚠️ Частично | Нужны stats endpoints |
| Analytics | ⚠️ Частично | Нужны analytics endpoints |
| Файловый менеджер | ✅ Готово | Полностью интегрировано |
| Системное администрирование | ⚠️ Частично | Нужны admin endpoints |
