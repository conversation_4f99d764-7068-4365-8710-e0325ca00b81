import apiClient from './api';
import { mockDataService } from './mockData';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

export const usersService = {
  // Get all users with filters
  async getUsers(filters = {}) {
    try {
      if (USE_MOCK) {
        return await mockDataService.getUsers(filters);
      }

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await apiClient.get(`/admin/users?${params}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки пользователей');
      throw error;
    }
  },

  // Get user details
  async getUserDetails(userId) {
    try {
      if (USE_MOCK) {
        const users = await mockDataService.getUsers();
        const user = users.users?.find(u => u.id === userId);
        if (!user) {
          throw new Error('User not found');
        }
        return { user };
      }

      const response = await apiClient.get(`/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки данных пользователя');
      throw error;
    }
  },

  // Update user status
  async updateUserStatus(userId, status, reason) {
    try {
      if (USE_MOCK) {
        toast.success('Статус пользователя обновлен');
        return {
          message: 'User status updated successfully',
          user: { id: userId, status, reason }
        };
      }

      const response = await apiClient.put(`/admin/users/${userId}/status`, {
        status,
        reason
      });
      toast.success('Статус пользователя обновлен');
      return response.data;
    } catch (error) {
      toast.error('Ошибка обновления статуса пользователя');
      throw error;
    }
  },

  // Delete user
  async deleteUser(userId) {
    try {
      if (USE_MOCK) {
        toast.success('Пользователь удален');
        return { message: 'User deleted successfully' };
      }

      const response = await apiClient.delete(`/admin/users/${userId}`);
      toast.success('Пользователь удален');
      return response.data;
    } catch (error) {
      toast.error('Ошибка удаления пользователя');
      throw error;
    }
  },

  // Get user progress
  async getUserProgress(userId) {
    try {
      if (USE_MOCK) {
        return {
          user_id: userId,
          passed_modules: [
            {
              module_id: 1,
              module_name: "Базовые приветствия",
              completed_at: "2024-01-01T15:30:00Z",
              best_time: "00:03:45",
              attempts: 2
            }
          ],
          total_modules: 10,
          completion_percentage: 10.0
        };
      }

      const response = await apiClient.get(`/module-user-progress/${userId}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки прогресса пользователя');
      throw error;
    }
  },

  // Get user streak
  async getUserStreak(userId) {
    try {
      if (USE_MOCK) {
        return {
          user_id: userId,
          current_streak: 7,
          max_streak: 15,
          last_activity: "2024-01-01T12:00:00Z"
        };
      }

      const response = await apiClient.get(`/progress/streak/${userId}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки серии дней пользователя');
      throw error;
    }
  },

  // Get user achievements
  async getUserAchievements(userId) {
    try {
      if (USE_MOCK) {
        return {
          user_id: userId,
          achievements: [
            {
              achievement_id: 1,
              user_id: userId,
              progress: 100,
              achieved: true,
              achievement: {
                id: 1,
                name: "Первые шаги",
                description: "Завершите первый модуль",
                type: "module_completion",
                target: 1,
                created_at: "2024-01-01T12:00:00Z"
              }
            }
          ],
          total_achievements: 15,
          unlocked_count: 3
        };
      }

      const response = await apiClient.get(`/achievements/${userId}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки достижений пользователя');
      throw error;
    }
  },

  // Save user progress
  async saveUserProgress(progressData) {
    try {
      if (USE_MOCK) {
        toast.success('Прогресс сохранен');
        return {
          progress: {
            id: Date.now(),
            ...progressData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        };
      }

      const response = await apiClient.post('/progress/save', progressData);
      toast.success('Прогресс сохранен');
      return response.data;
    } catch (error) {
      toast.error('Ошибка сохранения прогресса');
      throw error;
    }
  }
};

export default usersService;
