import apiClient from './api';
import toast from 'react-hot-toast';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

export const filesService = {
  // Upload single audio file
  async uploadAudio(audioFile, onProgress = null) {
    try {
      if (USE_MOCK) {
        // Mock upload with progress simulation
        if (onProgress) {
          for (let i = 0; i <= 100; i += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            onProgress({ loaded: i, total: 100 });
          }
        }

        const mockResponse = {
          message: "Audio file uploaded successfully",
          file_url: `http://minio:9000/klingo-audio/mock-${Date.now()}.mp3`,
          file_name: `mock-${Date.now()}.mp3`,
          file_size: audioFile.size,
          content_type: audioFile.type
        };

        toast.success('Аудио файл загружен успешно');
        return mockResponse;
      }

      const formData = new FormData();
      formData.append('audio', audioFile);

      const response = await apiClient.post('/files/upload/audio', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            onProgress(progressEvent);
          }
        }
      });

      toast.success('Аудио файл загружен успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки аудио файла');
      throw error;
    }
  },

  // Upload single image file
  async uploadImage(imageFile, onProgress = null) {
    try {
      if (USE_MOCK) {
        // Mock upload with progress simulation
        if (onProgress) {
          for (let i = 0; i <= 100; i += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            onProgress({ loaded: i, total: 100 });
          }
        }

        const mockResponse = {
          message: "Image file uploaded successfully",
          file_url: `http://minio:9000/klingo-images/mock-${Date.now()}.jpg`,
          file_name: `mock-${Date.now()}.jpg`,
          file_size: imageFile.size,
          content_type: imageFile.type
        };

        toast.success('Изображение загружено успешно');
        return mockResponse;
      }

      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await apiClient.post('/files/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            onProgress(progressEvent);
          }
        }
      });

      toast.success('Изображение загружено успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки изображения');
      throw error;
    }
  },

  // Upload multiple files
  async uploadMultiple(files, onProgress = null) {
    try {
      if (USE_MOCK) {
        // Mock multiple upload
        if (onProgress) {
          for (let i = 0; i <= 100; i += 5) {
            await new Promise(resolve => setTimeout(resolve, 50));
            onProgress({ loaded: i, total: 100 });
          }
        }

        const mockResponse = {
          message: "Files processed",
          uploaded_files: files.map((file, index) => ({
            file_name: `mock-${Date.now()}-${index}.${file.name.split('.').pop()}`,
            file_url: `http://minio:9000/klingo-${file.type.startsWith('audio') ? 'audio' : 'images'}/mock-${Date.now()}-${index}.${file.name.split('.').pop()}`,
            file_type: file.type.startsWith('audio') ? 'audio' : 'image'
          })),
          errors: [],
          total_uploaded: files.length,
          total_errors: 0
        };

        toast.success(`${files.length} файлов загружено успешно`);
        return mockResponse;
      }

      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await apiClient.post('/files/upload/multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            onProgress(progressEvent);
          }
        }
      });

      const { uploaded_files, total_uploaded, total_errors } = response.data;

      if (total_errors > 0) {
        toast.warning(`${total_uploaded} файлов загружено, ${total_errors} ошибок`);
      } else {
        toast.success(`${total_uploaded} файлов загружено успешно`);
      }

      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки файлов');
      throw error;
    }
  },

  // Get files list
  async getFilesList(bucket, prefix = '') {
    try {
      if (USE_MOCK) {
        const mockFiles = [
          'mock-audio-1.mp3',
          'mock-audio-2.wav',
          'mock-image-1.jpg',
          'mock-image-2.png',
          'folder/mock-audio-3.ogg'
        ];

        return {
          bucket,
          prefix,
          files: mockFiles.filter(file =>
            bucket === 'klingo-audio' ?
              file.includes('audio') || file.endsWith('.mp3') || file.endsWith('.wav') || file.endsWith('.ogg') :
              file.includes('image') || file.endsWith('.jpg') || file.endsWith('.png')
          ),
          count: mockFiles.length
        };
      }

      const params = new URLSearchParams({ bucket });
      if (prefix) params.append('prefix', prefix);

      const response = await apiClient.get(`/files/list?${params}`);
      return response.data;
    } catch (error) {
      toast.error('Ошибка загрузки списка файлов');
      throw error;
    }
  },

  // Delete file
  async deleteFile(fileUrl) {
    try {
      if (USE_MOCK) {
        toast.success('Файл удален успешно');
        return {
          message: "File deleted successfully",
          deleted_url: fileUrl
        };
      }

      const response = await apiClient.delete(`/files/delete?url=${encodeURIComponent(fileUrl)}`);
      toast.success('Файл удален успешно');
      return response.data;
    } catch (error) {
      toast.error('Ошибка удаления файла');
      throw error;
    }
  },

  // Get audio files
  async getAudioFiles(prefix = '') {
    return this.getFilesList('klingo-audio', prefix);
  },

  // Get image files
  async getImageFiles(prefix = '') {
    return this.getFilesList('klingo-images', prefix);
  },

  // Get all files with optional filters
  async getFiles(params = {}) {
    const response = await apiClient.get('/files', { params });
    return response.data;
  },

  // Get storage statistics
  async getStorageStats() {
    const response = await apiClient.get('/files/stats');
    return response.data;
  },

  // Get buckets list
  async getBuckets() {
    const response = await apiClient.get('/files/buckets');
    return response.data;
  },

  // Upload any file type
  async uploadFile(file, bucket = 'default') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bucket', bucket);

    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // File validation
  validateFile(file, options = {}) {
    const errors = [];
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [],
      allowedExtensions = []
    } = options;

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
    }

    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }

    // Check file extension
    if (allowedExtensions.length > 0) {
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        errors.push(`File extension .${extension} is not allowed`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

export default filesService;
